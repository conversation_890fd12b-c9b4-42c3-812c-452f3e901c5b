# نظام إدارة المبيعات - Sales Management System

## وصف النظام
نظام شامل لإدارة المبيعات مطور بلغة C# باستخدام Windows Forms وقاعدة بيانات SQLite. النظام يدعم اللغة العربية بالكامل ويتضمن جميع الميزات الأساسية لإدارة المبيعات.

## الميزات الرئيسية

### 1. إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تتبع المخزون والكميات
- إدارة الأسعار (شراء وبيع)
- نظام تنبيهات للمخزون المنخفض
- دعم الباركود
- تصنيف المنتجات

### 2. إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- دعم العملاء الأفراد والشركات
- إدارة الحدود الائتمانية
- تتبع تاريخ المشتريات
- معلومات الاتصال والعناوين

### 3. نظام المبيعات
- إنشاء فواتير المبيعات
- حساب الضرائب تلقائياً (15% ضريبة القيمة المضافة السعودية)
- دعم طرق دفع متعددة
- إدارة الخصومات
- تتبع حالة الفواتير

### 4. الواجهة والتصميم
- واجهة باللغة العربية
- دعم الكتابة من اليمين إلى اليسار (RTL)
- تصميم سهل الاستخدام
- نظام بحث متقدم

## المتطلبات التقنية

### متطلبات النظام
- Windows 7 أو أحدث
- .NET Framework 4.8
- 100 MB مساحة فارغة على القرص الصلب
- 2 GB ذاكرة وصول عشوائي (مُوصى به)

### متطلبات التطوير
- Visual Studio 2019 أو أحدث
- .NET Framework 4.8 SDK
- System.Data.SQLite NuGet Package

## هيكل المشروع

```
SalesSystemProject/
├── SalesSystem.sln              # ملف الحل الرئيسي
├── SalesSystem/                 # مجلد المشروع الرئيسي
│   ├── SalesSystem.csproj       # ملف المشروع
│   ├── Program.cs               # نقطة دخول التطبيق
│   ├── App.config               # إعدادات التطبيق
│   ├── Models/                  # نماذج البيانات
│   │   ├── Product.cs           # نموذج المنتج
│   │   ├── Customer.cs          # نموذج العميل
│   │   ├── Sale.cs              # نموذج المبيعات
│   │   └── SaleItem.cs          # نموذج عنصر المبيعات
│   ├── Data/                    # طبقة الوصول للبيانات
│   │   ├── DatabaseHelper.cs    # مساعد قاعدة البيانات
│   │   ├── ProductRepository.cs # مستودع المنتجات
│   │   └── CustomerRepository.cs# مستودع العملاء
│   ├── Forms/                   # نماذج الواجهة
│   │   ├── MainForm.cs          # النموذج الرئيسي
│   │   ├── ProductsForm.cs      # نموذج المنتجات
│   │   ├── CustomersForm.cs     # نموذج العملاء
│   │   └── SalesForm.cs         # نموذج المبيعات
│   ├── Properties/              # خصائص المشروع
│   ├── Reports/                 # التقارير (للتطوير المستقبلي)
│   └── Resources/               # الموارد والصور
├── تشغيل_النظام.bat             # ملف تشغيل سريع
└── README.md                    # هذا الملف
```

## طريقة التشغيل

### الطريقة الأولى: باستخدام Visual Studio
1. افتح ملف `SalesSystem.sln` في Visual Studio
2. اضغط F5 أو اختر Debug > Start Debugging
3. سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول

### الطريقة الثانية: باستخدام ملف التشغيل
1. انقر نقراً مزدوجاً على ملف `تشغيل_النظام.bat`
2. سيتم فتح المشروع في Visual Studio تلقائياً

## قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite المحلية التي يتم إنشاؤها تلقائياً. الجداول الرئيسية:

- **Products**: جدول المنتجات
- **Customers**: جدول العملاء  
- **Sales**: جدول المبيعات
- **SaleItems**: جدول عناصر المبيعات

## الإعدادات الافتراضية

- **ضريبة القيمة المضافة**: 15% (حسب النظام السعودي)
- **العملة**: ريال سعودي
- **ترقيم الفواتير**: INV + التاريخ والوقت
- **العميل الافتراضي**: عميل نقدي

## التطوير المستقبلي

### ميزات مخططة للإضافة:
- [ ] نظام التقارير المتقدم
- [ ] طباعة الفواتير
- [ ] نسخ احتياطي واستعادة البيانات
- [ ] إدارة المستخدمين والصلاحيات
- [ ] تكامل مع أنظمة المحاسبة
- [ ] تطبيق ويب مصاحب
- [ ] دعم قواعد بيانات أخرى (SQL Server, MySQL)

## الدعم والمساعدة

### المشاكل الشائعة:
1. **خطأ في قاعدة البيانات**: تأكد من وجود صلاحيات الكتابة في مجلد التطبيق
2. **مشاكل الخطوط العربية**: تأكد من تثبيت خط Tahoma
3. **بطء في الأداء**: تأكد من توفر مساحة كافية على القرص الصلب

### للحصول على المساعدة:
- راجع ملفات الكود للتفاصيل التقنية
- تحقق من رسائل الخطأ في النظام
- استخدم أدوات التصحيح في Visual Studio

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

## معلومات المطور

- **اللغة**: C# .NET Framework 4.8
- **قاعدة البيانات**: SQLite
- **الواجهة**: Windows Forms
- **التاريخ**: 2024
- **الإصدار**: 1.0

---

**ملاحظة**: هذا النظام جاهز للاستخدام الأساسي ويمكن تطويره وتخصيصه حسب احتياجات العمل المحددة.
