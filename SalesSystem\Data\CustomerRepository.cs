using System;
using System.Collections.Generic;
using System.Data.SQLite;
using SalesSystem.Models;

namespace SalesSystem.Data
{
    /// <summary>
    /// مستودع العملاء
    /// </summary>
    public class CustomerRepository
    {
        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        public List<Customer> GetAll()
        {
            var customers = new List<Customer>();
            var query = "SELECT * FROM Customers ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        customers.Add(MapFromReader(reader));
                    }
                }
            }

            return customers;
        }

        /// <summary>
        /// الحصول على العملاء النشطين فقط
        /// </summary>
        public List<Customer> GetActive()
        {
            var customers = new List<Customer>();
            var query = "SELECT * FROM Customers WHERE IsActive = 1 ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        customers.Add(MapFromReader(reader));
                    }
                }
            }

            return customers;
        }

        /// <summary>
        /// الحصول على عميل بالمعرف
        /// </summary>
        public Customer GetById(int id)
        {
            var query = "SELECT * FROM Customers WHERE Id = @Id";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return MapFromReader(reader);
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// الحصول على عميل بالكود
        /// </summary>
        public Customer GetByCode(string code)
        {
            var query = "SELECT * FROM Customers WHERE Code = @Code";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Code", code);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return MapFromReader(reader);
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// البحث في العملاء
        /// </summary>
        public List<Customer> Search(string searchTerm)
        {
            var customers = new List<Customer>();
            var query = @"SELECT * FROM Customers 
                         WHERE (Name LIKE @SearchTerm OR Code LIKE @SearchTerm OR Phone LIKE @SearchTerm)
                         AND IsActive = 1 
                         ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(MapFromReader(reader));
                        }
                    }
                }
            }

            return customers;
        }

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        public int Add(Customer customer)
        {
            var query = @"INSERT INTO Customers 
                         (Code, Name, Type, IdentityNumber, TaxNumber, Phone, Phone2, Email,
                          Address, City, Region, PostalCode, Country, CreditLimit, CurrentBalance,
                          DiscountPercentage, IsActive, CreatedDate, LastModified, Notes)
                         VALUES 
                         (@Code, @Name, @Type, @IdentityNumber, @TaxNumber, @Phone, @Phone2, @Email,
                          @Address, @City, @Region, @PostalCode, @Country, @CreditLimit, @CurrentBalance,
                          @DiscountPercentage, @IsActive, @CreatedDate, @LastModified, @Notes);
                         SELECT last_insert_rowid();";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    AddParameters(command, customer);
                    var result = command.ExecuteScalar();
                    return Convert.ToInt32(result);
                }
            }
        }

        /// <summary>
        /// تحديث عميل
        /// </summary>
        public bool Update(Customer customer)
        {
            var query = @"UPDATE Customers SET 
                         Code = @Code, Name = @Name, Type = @Type, IdentityNumber = @IdentityNumber,
                         TaxNumber = @TaxNumber, Phone = @Phone, Phone2 = @Phone2, Email = @Email,
                         Address = @Address, City = @City, Region = @Region, PostalCode = @PostalCode,
                         Country = @Country, CreditLimit = @CreditLimit, CurrentBalance = @CurrentBalance,
                         DiscountPercentage = @DiscountPercentage, IsActive = @IsActive,
                         LastModified = @LastModified, Notes = @Notes
                         WHERE Id = @Id";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    AddParameters(command, customer);
                    command.Parameters.AddWithValue("@Id", customer.Id);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        public bool Delete(int id)
        {
            var query = "DELETE FROM Customers WHERE Id = @Id";
            return DatabaseHelper.ExecuteNonQuery(query, new SQLiteParameter("@Id", id)) > 0;
        }

        /// <summary>
        /// تحديث رصيد العميل
        /// </summary>
        public bool UpdateBalance(int customerId, decimal newBalance)
        {
            var query = @"UPDATE Customers SET 
                         CurrentBalance = @Balance, 
                         LastModified = @LastModified 
                         WHERE Id = @Id";
            
            return DatabaseHelper.ExecuteNonQuery(query,
                new SQLiteParameter("@Balance", newBalance),
                new SQLiteParameter("@LastModified", DateTime.Now),
                new SQLiteParameter("@Id", customerId)) > 0;
        }

        /// <summary>
        /// تحديث إجمالي المشتريات وتاريخ آخر شراء
        /// </summary>
        public bool UpdatePurchaseInfo(int customerId, decimal totalPurchases, DateTime lastPurchaseDate)
        {
            var query = @"UPDATE Customers SET 
                         TotalPurchases = @TotalPurchases,
                         LastPurchaseDate = @LastPurchaseDate,
                         LastModified = @LastModified 
                         WHERE Id = @Id";
            
            return DatabaseHelper.ExecuteNonQuery(query,
                new SQLiteParameter("@TotalPurchases", totalPurchases),
                new SQLiteParameter("@LastPurchaseDate", lastPurchaseDate),
                new SQLiteParameter("@LastModified", DateTime.Now),
                new SQLiteParameter("@Id", customerId)) > 0;
        }

        /// <summary>
        /// تحويل من قارئ البيانات إلى كائن العميل
        /// </summary>
        private Customer MapFromReader(SQLiteDataReader reader)
        {
            return new Customer
            {
                Id = Convert.ToInt32(reader["Id"]),
                Code = reader["Code"].ToString(),
                Name = reader["Name"].ToString(),
                Type = (CustomerType)Convert.ToInt32(reader["Type"]),
                IdentityNumber = reader["IdentityNumber"].ToString(),
                TaxNumber = reader["TaxNumber"].ToString(),
                Phone = reader["Phone"].ToString(),
                Phone2 = reader["Phone2"].ToString(),
                Email = reader["Email"].ToString(),
                Address = reader["Address"].ToString(),
                City = reader["City"].ToString(),
                Region = reader["Region"].ToString(),
                PostalCode = reader["PostalCode"].ToString(),
                Country = reader["Country"].ToString(),
                CreditLimit = Convert.ToDecimal(reader["CreditLimit"]),
                CurrentBalance = Convert.ToDecimal(reader["CurrentBalance"]),
                DiscountPercentage = Convert.ToDecimal(reader["DiscountPercentage"]),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                LastModified = Convert.ToDateTime(reader["LastModified"]),
                LastPurchaseDate = reader["LastPurchaseDate"] == DBNull.Value ? 
                    (DateTime?)null : Convert.ToDateTime(reader["LastPurchaseDate"]),
                TotalPurchases = Convert.ToDecimal(reader["TotalPurchases"]),
                Notes = reader["Notes"].ToString()
            };
        }

        /// <summary>
        /// إضافة المعاملات للاستعلام
        /// </summary>
        private void AddParameters(SQLiteCommand command, Customer customer)
        {
            command.Parameters.AddWithValue("@Code", customer.Code);
            command.Parameters.AddWithValue("@Name", customer.Name);
            command.Parameters.AddWithValue("@Type", (int)customer.Type);
            command.Parameters.AddWithValue("@IdentityNumber", customer.IdentityNumber ?? "");
            command.Parameters.AddWithValue("@TaxNumber", customer.TaxNumber ?? "");
            command.Parameters.AddWithValue("@Phone", customer.Phone);
            command.Parameters.AddWithValue("@Phone2", customer.Phone2 ?? "");
            command.Parameters.AddWithValue("@Email", customer.Email ?? "");
            command.Parameters.AddWithValue("@Address", customer.Address ?? "");
            command.Parameters.AddWithValue("@City", customer.City ?? "");
            command.Parameters.AddWithValue("@Region", customer.Region ?? "");
            command.Parameters.AddWithValue("@PostalCode", customer.PostalCode ?? "");
            command.Parameters.AddWithValue("@Country", customer.Country ?? "المملكة العربية السعودية");
            command.Parameters.AddWithValue("@CreditLimit", customer.CreditLimit);
            command.Parameters.AddWithValue("@CurrentBalance", customer.CurrentBalance);
            command.Parameters.AddWithValue("@DiscountPercentage", customer.DiscountPercentage);
            command.Parameters.AddWithValue("@IsActive", customer.IsActive);
            command.Parameters.AddWithValue("@CreatedDate", customer.CreatedDate);
            command.Parameters.AddWithValue("@LastModified", DateTime.Now);
            command.Parameters.AddWithValue("@Notes", customer.Notes ?? "");
        }
    }
}
