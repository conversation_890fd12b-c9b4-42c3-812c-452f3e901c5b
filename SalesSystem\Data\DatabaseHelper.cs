using System;
using System.Data.SQLite;
using System.IO;
using System.Configuration;

namespace SalesSystem.Data
{
    /// <summary>
    /// مساعد قاعدة البيانات
    /// </summary>
    public static class DatabaseHelper
    {
        private static string _connectionString;

        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    _connectionString = ConfigurationManager.ConnectionStrings["SalesSystemDB"]?.ConnectionString
                                     ?? "Data Source=SalesSystem.db;Version=3;";
                }
                return _connectionString;
            }
        }

        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    CreateTables(connection);
                    InsertDefaultData(connection);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء الجداول
        /// </summary>
        private static void CreateTables(SQLiteConnection connection)
        {
            // جدول المنتجات
            var createProductsTable = @"
                CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Code TEXT NOT NULL UNIQUE,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    Category TEXT,
                    PurchasePrice DECIMAL(10,2) DEFAULT 0,
                    SalePrice DECIMAL(10,2) DEFAULT 0,
                    StockQuantity INTEGER DEFAULT 0,
                    MinStockLevel INTEGER DEFAULT 0,
                    Unit TEXT DEFAULT 'قطعة',
                    Barcode TEXT,
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastModified DATETIME DEFAULT CURRENT_TIMESTAMP,
                    Notes TEXT,
                    ImagePath TEXT
                )";

            // جدول العملاء
            var createCustomersTable = @"
                CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Code TEXT NOT NULL UNIQUE,
                    Name TEXT NOT NULL,
                    Type INTEGER DEFAULT 1,
                    IdentityNumber TEXT,
                    TaxNumber TEXT,
                    Phone TEXT NOT NULL,
                    Phone2 TEXT,
                    Email TEXT,
                    Address TEXT,
                    City TEXT,
                    Region TEXT,
                    PostalCode TEXT,
                    Country TEXT DEFAULT 'المملكة العربية السعودية',
                    CreditLimit DECIMAL(10,2) DEFAULT 0,
                    CurrentBalance DECIMAL(10,2) DEFAULT 0,
                    DiscountPercentage DECIMAL(5,2) DEFAULT 0,
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastModified DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastPurchaseDate DATETIME,
                    TotalPurchases DECIMAL(10,2) DEFAULT 0,
                    Notes TEXT
                )";

            // جدول المبيعات
            var createSalesTable = @"
                CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL UNIQUE,
                    CustomerId INTEGER NOT NULL,
                    SaleDate DATETIME NOT NULL,
                    DueDate DATETIME,
                    Status INTEGER DEFAULT 1,
                    PaymentMethod INTEGER DEFAULT 1,
                    SubTotal DECIMAL(10,2) DEFAULT 0,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    DiscountPercentage DECIMAL(5,2) DEFAULT 0,
                    TaxAmount DECIMAL(10,2) DEFAULT 0,
                    TaxPercentage DECIMAL(5,2) DEFAULT 15,
                    TotalAmount DECIMAL(10,2) DEFAULT 0,
                    PaidAmount DECIMAL(10,2) DEFAULT 0,
                    RemainingAmount DECIMAL(10,2) DEFAULT 0,
                    Notes TEXT,
                    CreatedBy TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastModified DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
                )";

            // جدول عناصر المبيعات
            var createSaleItemsTable = @"
                CREATE TABLE IF NOT EXISTS SaleItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SaleId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity DECIMAL(10,3) NOT NULL,
                    UnitPrice DECIMAL(10,2) NOT NULL,
                    DiscountPercentage DECIMAL(5,2) DEFAULT 0,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    Total DECIMAL(10,2) NOT NULL,
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (SaleId) REFERENCES Sales(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            using (var command = new SQLiteCommand(createProductsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createCustomersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createSalesTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createSaleItemsTable, connection))
                command.ExecuteNonQuery();
        }

        /// <summary>
        /// إدراج البيانات الافتراضية
        /// </summary>
        private static void InsertDefaultData(SQLiteConnection connection)
        {
            // التحقق من وجود بيانات
            var checkQuery = "SELECT COUNT(*) FROM Customers";
            using (var command = new SQLiteCommand(checkQuery, connection))
            {
                var count = Convert.ToInt32(command.ExecuteScalar());
                if (count > 0) return; // البيانات موجودة بالفعل
            }

            // إدراج عميل افتراضي
            var insertCustomer = @"
                INSERT INTO Customers (Code, Name, Phone, Type, IsActive, CreatedDate, LastModified)
                VALUES ('C001', 'عميل نقدي', '0000000000', 1, 1, datetime('now'), datetime('now'))";

            using (var command = new SQLiteCommand(insertCustomer, connection))
                command.ExecuteNonQuery();

            // إدراج منتج تجريبي
            var insertProduct = @"
                INSERT INTO Products (Code, Name, SalePrice, PurchasePrice, StockQuantity, IsActive, CreatedDate, LastModified)
                VALUES ('P001', 'منتج تجريبي', 100.00, 80.00, 50, 1, datetime('now'), datetime('now'))";

            using (var command = new SQLiteCommand(insertProduct, connection))
                command.ExecuteNonQuery();
        }

        /// <summary>
        /// الحصول على اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns></returns>
        public static SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(ConnectionString);
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        public static int ExecuteNonQuery(string query, params SQLiteParameter[] parameters)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    if (parameters != null)
                        command.Parameters.AddRange(parameters);
                    
                    return command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// </summary>
        public static object ExecuteScalar(string query, params SQLiteParameter[] parameters)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    if (parameters != null)
                        command.Parameters.AddRange(parameters);
                    
                    return command.ExecuteScalar();
                }
            }
        }
    }
}
