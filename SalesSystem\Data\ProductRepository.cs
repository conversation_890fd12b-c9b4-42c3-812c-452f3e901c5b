using System;
using System.Collections.Generic;
using System.Data.SQLite;
using SalesSystem.Models;

namespace SalesSystem.Data
{
    /// <summary>
    /// مستودع المنتجات
    /// </summary>
    public class ProductRepository
    {
        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        public List<Product> GetAll()
        {
            var products = new List<Product>();
            var query = "SELECT * FROM Products ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        products.Add(MapFromReader(reader));
                    }
                }
            }

            return products;
        }

        /// <summary>
        /// الحصول على المنتجات النشطة فقط
        /// </summary>
        public List<Product> GetActive()
        {
            var products = new List<Product>();
            var query = "SELECT * FROM Products WHERE IsActive = 1 ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        products.Add(MapFromReader(reader));
                    }
                }
            }

            return products;
        }

        /// <summary>
        /// الحصول على منتج بالمعرف
        /// </summary>
        public Product GetById(int id)
        {
            var query = "SELECT * FROM Products WHERE Id = @Id";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return MapFromReader(reader);
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// الحصول على منتج بالكود
        /// </summary>
        public Product GetByCode(string code)
        {
            var query = "SELECT * FROM Products WHERE Code = @Code";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Code", code);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return MapFromReader(reader);
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// البحث في المنتجات
        /// </summary>
        public List<Product> Search(string searchTerm)
        {
            var products = new List<Product>();
            var query = @"SELECT * FROM Products 
                         WHERE (Name LIKE @SearchTerm OR Code LIKE @SearchTerm OR Barcode LIKE @SearchTerm)
                         AND IsActive = 1 
                         ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            products.Add(MapFromReader(reader));
                        }
                    }
                }
            }

            return products;
        }

        /// <summary>
        /// إضافة منتج جديد
        /// </summary>
        public int Add(Product product)
        {
            var query = @"INSERT INTO Products 
                         (Code, Name, Description, Category, PurchasePrice, SalePrice, 
                          StockQuantity, MinStockLevel, Unit, Barcode, IsActive, 
                          CreatedDate, LastModified, Notes, ImagePath)
                         VALUES 
                         (@Code, @Name, @Description, @Category, @PurchasePrice, @SalePrice,
                          @StockQuantity, @MinStockLevel, @Unit, @Barcode, @IsActive,
                          @CreatedDate, @LastModified, @Notes, @ImagePath);
                         SELECT last_insert_rowid();";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    AddParameters(command, product);
                    var result = command.ExecuteScalar();
                    return Convert.ToInt32(result);
                }
            }
        }

        /// <summary>
        /// تحديث منتج
        /// </summary>
        public bool Update(Product product)
        {
            var query = @"UPDATE Products SET 
                         Code = @Code, Name = @Name, Description = @Description, 
                         Category = @Category, PurchasePrice = @PurchasePrice, 
                         SalePrice = @SalePrice, StockQuantity = @StockQuantity,
                         MinStockLevel = @MinStockLevel, Unit = @Unit, Barcode = @Barcode,
                         IsActive = @IsActive, LastModified = @LastModified,
                         Notes = @Notes, ImagePath = @ImagePath
                         WHERE Id = @Id";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                {
                    AddParameters(command, product);
                    command.Parameters.AddWithValue("@Id", product.Id);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }

        /// <summary>
        /// حذف منتج
        /// </summary>
        public bool Delete(int id)
        {
            var query = "DELETE FROM Products WHERE Id = @Id";
            return DatabaseHelper.ExecuteNonQuery(query, new SQLiteParameter("@Id", id)) > 0;
        }

        /// <summary>
        /// تحديث كمية المخزون
        /// </summary>
        public bool UpdateStock(int productId, int newQuantity)
        {
            var query = "UPDATE Products SET StockQuantity = @Quantity, LastModified = @LastModified WHERE Id = @Id";
            return DatabaseHelper.ExecuteNonQuery(query,
                new SQLiteParameter("@Quantity", newQuantity),
                new SQLiteParameter("@LastModified", DateTime.Now),
                new SQLiteParameter("@Id", productId)) > 0;
        }

        /// <summary>
        /// الحصول على المنتجات التي تحتاج إعادة تموين
        /// </summary>
        public List<Product> GetLowStockProducts()
        {
            var products = new List<Product>();
            var query = "SELECT * FROM Products WHERE StockQuantity <= MinStockLevel AND IsActive = 1 ORDER BY Name";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        products.Add(MapFromReader(reader));
                    }
                }
            }

            return products;
        }

        /// <summary>
        /// تحويل من قارئ البيانات إلى كائن المنتج
        /// </summary>
        private Product MapFromReader(SQLiteDataReader reader)
        {
            return new Product
            {
                Id = Convert.ToInt32(reader["Id"]),
                Code = reader["Code"].ToString(),
                Name = reader["Name"].ToString(),
                Description = reader["Description"].ToString(),
                Category = reader["Category"].ToString(),
                PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                StockQuantity = Convert.ToInt32(reader["StockQuantity"]),
                MinStockLevel = Convert.ToInt32(reader["MinStockLevel"]),
                Unit = reader["Unit"].ToString(),
                Barcode = reader["Barcode"].ToString(),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                LastModified = Convert.ToDateTime(reader["LastModified"]),
                Notes = reader["Notes"].ToString(),
                ImagePath = reader["ImagePath"].ToString()
            };
        }

        /// <summary>
        /// إضافة المعاملات للاستعلام
        /// </summary>
        private void AddParameters(SQLiteCommand command, Product product)
        {
            command.Parameters.AddWithValue("@Code", product.Code);
            command.Parameters.AddWithValue("@Name", product.Name);
            command.Parameters.AddWithValue("@Description", product.Description ?? "");
            command.Parameters.AddWithValue("@Category", product.Category ?? "");
            command.Parameters.AddWithValue("@PurchasePrice", product.PurchasePrice);
            command.Parameters.AddWithValue("@SalePrice", product.SalePrice);
            command.Parameters.AddWithValue("@StockQuantity", product.StockQuantity);
            command.Parameters.AddWithValue("@MinStockLevel", product.MinStockLevel);
            command.Parameters.AddWithValue("@Unit", product.Unit ?? "قطعة");
            command.Parameters.AddWithValue("@Barcode", product.Barcode ?? "");
            command.Parameters.AddWithValue("@IsActive", product.IsActive);
            command.Parameters.AddWithValue("@CreatedDate", product.CreatedDate);
            command.Parameters.AddWithValue("@LastModified", DateTime.Now);
            command.Parameters.AddWithValue("@Notes", product.Notes ?? "");
            command.Parameters.AddWithValue("@ImagePath", product.ImagePath ?? "");
        }
    }
}
