using System;
using System.Collections.Generic;
using System.Data;
using SalesSystem.Models;

namespace SalesSystem.Data
{
    public class SaleRepository
    {
        private readonly DatabaseHelper _dbHelper;

        public SaleRepository()
        {
            _dbHelper = new DatabaseHelper();
        }

        public List<Sale> GetAllSales()
        {
            var sales = new List<Sale>();
            // TODO: Implement database query
            return sales;
        }

        public Sale GetSaleById(int id)
        {
            // TODO: Implement database query
            return null;
        }

        public bool AddSale(Sale sale)
        {
            // TODO: Implement database insert
            return true;
        }

        public bool UpdateSale(Sale sale)
        {
            // TODO: Implement database update
            return true;
        }

        public bool DeleteSale(int id)
        {
            // TODO: Implement database delete
            return true;
        }
    }
}
