using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using SalesSystem.Data;
using SalesSystem.Models;

namespace SalesSystem.Forms
{
    /// <summary>
    /// نموذج إدارة العملاء
    /// </summary>
    public partial class CustomersForm : Form
    {
        private CustomerRepository _customerRepository;
        private List<Customer> _customers;
        private Customer _currentCustomer;

        public CustomersForm()
        {
            InitializeComponent();
            _customerRepository = new CustomerRepository();
            this.Text = "إدارة العملاء";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void CustomersForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadCustomers();
                SetupDataGridView();
                SetupComboBoxes();
                ClearForm();
                EnableControls(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void LoadCustomers()
        {
            _customers = _customerRepository.GetAll();
            dgvCustomers.DataSource = _customers;
        }

        private void SetupDataGridView()
        {
            dgvCustomers.AutoGenerateColumns = false;
            dgvCustomers.Columns.Clear();

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Code",
                HeaderText = "الكود",
                Width = 100
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Name",
                HeaderText = "اسم العميل",
                Width = 200
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "TypeText",
                HeaderText = "النوع",
                Width = 100
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Phone",
                HeaderText = "الهاتف",
                Width = 120
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "City",
                HeaderText = "المدينة",
                Width = 100
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvCustomers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                DataPropertyName = "IsActive",
                HeaderText = "نشط",
                Width = 60
            });

            dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCustomers.MultiSelect = false;
        }

        private void SetupComboBoxes()
        {
            // إعداد قائمة نوع العميل
            cmbType.Items.Clear();
            cmbType.Items.Add(new { Text = "فرد", Value = CustomerType.Individual });
            cmbType.Items.Add(new { Text = "شركة", Value = CustomerType.Company });
            cmbType.DisplayMember = "Text";
            cmbType.ValueMember = "Value";
            cmbType.SelectedIndex = 0;
        }

        private void ClearForm()
        {
            txtCode.Clear();
            txtName.Clear();
            cmbType.SelectedIndex = 0;
            txtIdentityNumber.Clear();
            txtTaxNumber.Clear();
            txtPhone.Clear();
            txtPhone2.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            txtCity.Clear();
            txtRegion.Clear();
            txtPostalCode.Clear();
            txtCountry.Text = "المملكة العربية السعودية";
            txtCreditLimit.Clear();
            txtCurrentBalance.Clear();
            txtDiscountPercentage.Clear();
            txtNotes.Clear();
            chkIsActive.Checked = true;
            _currentCustomer = null;
        }

        private void LoadCustomerToForm(Customer customer)
        {
            if (customer == null) return;

            _currentCustomer = customer;
            txtCode.Text = customer.Code;
            txtName.Text = customer.Name;
            cmbType.SelectedValue = customer.Type;
            txtIdentityNumber.Text = customer.IdentityNumber;
            txtTaxNumber.Text = customer.TaxNumber;
            txtPhone.Text = customer.Phone;
            txtPhone2.Text = customer.Phone2;
            txtEmail.Text = customer.Email;
            txtAddress.Text = customer.Address;
            txtCity.Text = customer.City;
            txtRegion.Text = customer.Region;
            txtPostalCode.Text = customer.PostalCode;
            txtCountry.Text = customer.Country;
            txtCreditLimit.Text = customer.CreditLimit.ToString("F2");
            txtCurrentBalance.Text = customer.CurrentBalance.ToString("F2");
            txtDiscountPercentage.Text = customer.DiscountPercentage.ToString("F2");
            txtNotes.Text = customer.Notes;
            chkIsActive.Checked = customer.IsActive;
        }

        private Customer GetCustomerFromForm()
        {
            var customer = _currentCustomer ?? new Customer();

            customer.Code = txtCode.Text.Trim();
            customer.Name = txtName.Text.Trim();
            customer.Type = (CustomerType)cmbType.SelectedValue;
            customer.IdentityNumber = txtIdentityNumber.Text.Trim();
            customer.TaxNumber = txtTaxNumber.Text.Trim();
            customer.Phone = txtPhone.Text.Trim();
            customer.Phone2 = txtPhone2.Text.Trim();
            customer.Email = txtEmail.Text.Trim();
            customer.Address = txtAddress.Text.Trim();
            customer.City = txtCity.Text.Trim();
            customer.Region = txtRegion.Text.Trim();
            customer.PostalCode = txtPostalCode.Text.Trim();
            customer.Country = txtCountry.Text.Trim();
            
            if (decimal.TryParse(txtCreditLimit.Text, out decimal creditLimit))
                customer.CreditLimit = creditLimit;
            
            if (decimal.TryParse(txtCurrentBalance.Text, out decimal currentBalance))
                customer.CurrentBalance = currentBalance;
            
            if (decimal.TryParse(txtDiscountPercentage.Text, out decimal discountPercentage))
                customer.DiscountPercentage = discountPercentage;

            customer.Notes = txtNotes.Text.Trim();
            customer.IsActive = chkIsActive.Checked;

            return customer;
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("كود العميل مطلوب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("اسم العميل مطلوب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("رقم الهاتف مطلوب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            return true;
        }

        private void EnableControls(bool enabled)
        {
            txtCode.Enabled = enabled;
            txtName.Enabled = enabled;
            cmbType.Enabled = enabled;
            txtIdentityNumber.Enabled = enabled;
            txtTaxNumber.Enabled = enabled;
            txtPhone.Enabled = enabled;
            txtPhone2.Enabled = enabled;
            txtEmail.Enabled = enabled;
            txtAddress.Enabled = enabled;
            txtCity.Enabled = enabled;
            txtRegion.Enabled = enabled;
            txtPostalCode.Enabled = enabled;
            txtCountry.Enabled = enabled;
            txtCreditLimit.Enabled = enabled;
            txtCurrentBalance.Enabled = enabled;
            txtDiscountPercentage.Enabled = enabled;
            txtNotes.Enabled = enabled;
            chkIsActive.Enabled = enabled;

            btnSave.Enabled = enabled;
            btnCancel.Enabled = enabled;
        }

        // أحداث الأزرار
        private void btnNew_Click(object sender, EventArgs e)
        {
            ClearForm();
            EnableControls(true);
            txtCode.Focus();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.CurrentRow?.DataBoundItem is Customer customer)
            {
                LoadCustomerToForm(customer);
                EnableControls(true);
                txtName.Focus();
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var customer = GetCustomerFromForm();

                if (_currentCustomer == null)
                {
                    // إضافة عميل جديد
                    var id = _customerRepository.Add(customer);
                    if (id > 0)
                    {
                        MessageBox.Show("تم حفظ العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCustomers();
                        ClearForm();
                        EnableControls(false);
                    }
                }
                else
                {
                    // تحديث عميل موجود
                    if (_customerRepository.Update(customer))
                    {
                        MessageBox.Show("تم تحديث العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCustomers();
                        ClearForm();
                        EnableControls(false);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            ClearForm();
            EnableControls(false);
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.CurrentRow?.DataBoundItem is Customer customer)
            {
                if (MessageBox.Show($"هل تريد حذف العميل '{customer.Name}'؟", 
                                  "تأكيد الحذف", 
                                  MessageBoxButtons.YesNo, 
                                  MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    try
                    {
                        if (_customerRepository.Delete(customer.Id))
                        {
                            MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadCustomers();
                            ClearForm();
                            EnableControls(false);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchTerm))
                {
                    LoadCustomers();
                }
                else
                {
                    _customers = _customerRepository.Search(searchTerm);
                    dgvCustomers.DataSource = _customers;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearch_Click(sender, e);
            }
        }

        private void dgvCustomers_SelectionChanged(object sender, EventArgs e)
        {
            btnEdit.Enabled = dgvCustomers.CurrentRow != null;
            btnDelete.Enabled = dgvCustomers.CurrentRow != null;
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
