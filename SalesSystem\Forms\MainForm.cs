using System;
using System.Windows.Forms;

namespace SalesSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            this.Text = "نظام إدارة المبيعات - الصفحة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("مرحباً بك في نظام إدارة المبيعات",
                              "نظام المبيعات",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل النظام: " + ex.Message,
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }


    }
}
