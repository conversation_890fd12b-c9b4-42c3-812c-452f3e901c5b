using System;
using System.Windows.Forms;

namespace SalesSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            this.Text = "نظام إدارة المبيعات - الصفحة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = System.Drawing.Color.LightBlue;

            // إضافة تسمية ترحيبية
            var lblWelcome = new Label();
            lblWelcome.Text = "مرحباً بك في نظام إدارة المبيعات";
            lblWelcome.Font = new System.Drawing.Font("Arial", 24, System.Drawing.FontStyle.Bold);
            lblWelcome.ForeColor = System.Drawing.Color.DarkBlue;
            lblWelcome.AutoSize = true;
            lblWelcome.Location = new System.Drawing.Point(50, 50);
            this.Controls.Add(lblWelcome);

            // إضافة أزرار للوظائف الرئيسية
            var btnProducts = new Button();
            btnProducts.Text = "إدارة المنتجات";
            btnProducts.Size = new System.Drawing.Size(200, 50);
            btnProducts.Location = new System.Drawing.Point(50, 150);
            btnProducts.Font = new System.Drawing.Font("Arial", 12);
            btnProducts.Click += BtnProducts_Click;
            this.Controls.Add(btnProducts);

            var btnCustomers = new Button();
            btnCustomers.Text = "إدارة العملاء";
            btnCustomers.Size = new System.Drawing.Size(200, 50);
            btnCustomers.Location = new System.Drawing.Point(300, 150);
            btnCustomers.Font = new System.Drawing.Font("Arial", 12);
            btnCustomers.Click += BtnCustomers_Click;
            this.Controls.Add(btnCustomers);

            var btnSales = new Button();
            btnSales.Text = "إدارة المبيعات";
            btnSales.Size = new System.Drawing.Size(200, 50);
            btnSales.Location = new System.Drawing.Point(550, 150);
            btnSales.Font = new System.Drawing.Font("Arial", 12);
            btnSales.Click += BtnSales_Click;
            this.Controls.Add(btnSales);
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("مرحباً بك في نظام إدارة المبيعات",
                              "نظام المبيعات",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل النظام: " + ex.Message,
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }

        private void BtnProducts_Click(object sender, EventArgs e)
        {
            try
            {
                var productsForm = new ProductsForm();
                productsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نموذج المنتجات: " + ex.Message,
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }

        private void BtnCustomers_Click(object sender, EventArgs e)
        {
            try
            {
                var customersForm = new CustomersForm();
                customersForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نموذج العملاء: " + ex.Message,
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }

        private void BtnSales_Click(object sender, EventArgs e)
        {
            try
            {
                var salesForm = new SalesForm();
                salesForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نموذج المبيعات: " + ex.Message,
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }


    }
}
