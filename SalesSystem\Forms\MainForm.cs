using System;
using System.Windows.Forms;
using SalesSystem.Data;

namespace SalesSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            this.Text = "نظام إدارة المبيعات - الصفحة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                // تحديث شريط الحالة
                UpdateStatusBar();
                
                // تحديث الإحصائيات
                UpdateDashboard();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void UpdateStatusBar()
        {
            lblStatus.Text = $"تم تحميل النظام بنجاح - {DateTime.Now:yyyy/MM/dd HH:mm}";
        }

        private void UpdateDashboard()
        {
            try
            {
                var productRepo = new ProductRepository();
                var products = productRepo.GetActive();
                var lowStockProducts = productRepo.GetLowStockProducts();

                lblTotalProducts.Text = $"إجمالي المنتجات: {products.Count}";
                lblLowStockProducts.Text = $"منتجات تحتاج تموين: {lowStockProducts.Count}";
                
                if (lowStockProducts.Count > 0)
                {
                    lblLowStockProducts.ForeColor = System.Drawing.Color.Red;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Warning);
            }
        }

        // أحداث القوائم
        private void btnProducts_Click(object sender, EventArgs e)
        {
            try
            {
                var productsForm = new ProductsForm();
                productsForm.ShowDialog();
                UpdateDashboard(); // تحديث الإحصائيات بعد إغلاق النموذج
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج المنتجات: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void btnCustomers_Click(object sender, EventArgs e)
        {
            try
            {
                var customersForm = new CustomersForm();
                customersForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج العملاء: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void btnSales_Click(object sender, EventArgs e)
        {
            try
            {
                var salesForm = new SalesForm();
                salesForm.ShowDialog();
                UpdateDashboard(); // تحديث الإحصائيات بعد إغلاق النموذج
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج المبيعات: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void btnReports_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نموذج التقارير قيد التطوير", 
                          "معلومات", 
                          MessageBoxButtons.OK, 
                          MessageBoxIcon.Information);
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نموذج الإعدادات قيد التطوير", 
                          "معلومات", 
                          MessageBoxButtons.OK, 
                          MessageBoxIcon.Information);
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق التطبيق؟", 
                              "تأكيد الإغلاق", 
                              MessageBoxButtons.YesNo, 
                              MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق التطبيق؟", 
                              "تأكيد الإغلاق", 
                              MessageBoxButtons.YesNo, 
                              MessageBoxIcon.Question) == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        // تحديث دوري للإحصائيات
        private void timerRefresh_Tick(object sender, EventArgs e)
        {
            UpdateStatusBar();
        }
    }
}
