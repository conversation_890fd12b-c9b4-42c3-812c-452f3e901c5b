using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using SalesSystem.Data;
using SalesSystem.Models;

namespace SalesSystem.Forms
{
    /// <summary>
    /// نموذج إدارة المنتجات
    /// </summary>
    public partial class ProductsForm : Form
    {
        private ProductRepository _productRepository;
        private List<Product> _products;
        private Product _currentProduct;

        public ProductsForm()
        {
            InitializeComponent();
            _productRepository = new ProductRepository();
            this.Text = "إدارة المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void ProductsForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadProducts();
                SetupDataGridView();
                ClearForm();
                EnableControls(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void LoadProducts()
        {
            _products = _productRepository.GetAll();
            dgvProducts.DataSource = _products;
        }

        private void SetupDataGridView()
        {
            dgvProducts.AutoGenerateColumns = false;
            dgvProducts.Columns.Clear();

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Code",
                HeaderText = "الكود",
                Width = 100
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Name",
                HeaderText = "اسم المنتج",
                Width = 200
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Category",
                HeaderText = "الفئة",
                Width = 120
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "SalePrice",
                HeaderText = "سعر البيع",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "StockQuantity",
                HeaderText = "الكمية",
                Width = 80
            });

            dgvProducts.Columns.Add(new DataGridViewCheckBoxColumn
            {
                DataPropertyName = "IsActive",
                HeaderText = "نشط",
                Width = 60
            });

            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.MultiSelect = false;
        }

        private void ClearForm()
        {
            txtCode.Clear();
            txtName.Clear();
            txtDescription.Clear();
            txtCategory.Clear();
            txtPurchasePrice.Clear();
            txtSalePrice.Clear();
            txtStockQuantity.Clear();
            txtMinStockLevel.Clear();
            txtUnit.Text = "قطعة";
            txtBarcode.Clear();
            txtNotes.Clear();
            chkIsActive.Checked = true;
            _currentProduct = null;
        }

        private void LoadProductToForm(Product product)
        {
            if (product == null) return;

            _currentProduct = product;
            txtCode.Text = product.Code;
            txtName.Text = product.Name;
            txtDescription.Text = product.Description;
            txtCategory.Text = product.Category;
            txtPurchasePrice.Text = product.PurchasePrice.ToString("F2");
            txtSalePrice.Text = product.SalePrice.ToString("F2");
            txtStockQuantity.Text = product.StockQuantity.ToString();
            txtMinStockLevel.Text = product.MinStockLevel.ToString();
            txtUnit.Text = product.Unit;
            txtBarcode.Text = product.Barcode;
            txtNotes.Text = product.Notes;
            chkIsActive.Checked = product.IsActive;
        }

        private Product GetProductFromForm()
        {
            var product = _currentProduct ?? new Product();

            product.Code = txtCode.Text.Trim();
            product.Name = txtName.Text.Trim();
            product.Description = txtDescription.Text.Trim();
            product.Category = txtCategory.Text.Trim();
            
            if (decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice))
                product.PurchasePrice = purchasePrice;
            
            if (decimal.TryParse(txtSalePrice.Text, out decimal salePrice))
                product.SalePrice = salePrice;
            
            if (int.TryParse(txtStockQuantity.Text, out int stockQuantity))
                product.StockQuantity = stockQuantity;
            
            if (int.TryParse(txtMinStockLevel.Text, out int minStockLevel))
                product.MinStockLevel = minStockLevel;

            product.Unit = txtUnit.Text.Trim();
            product.Barcode = txtBarcode.Text.Trim();
            product.Notes = txtNotes.Text.Trim();
            product.IsActive = chkIsActive.Checked;

            return product;
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("كود المنتج مطلوب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("اسم المنتج مطلوب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (!decimal.TryParse(txtSalePrice.Text, out decimal salePrice) || salePrice < 0)
            {
                MessageBox.Show("سعر البيع غير صحيح", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSalePrice.Focus();
                return false;
            }

            return true;
        }

        private void EnableControls(bool enabled)
        {
            txtCode.Enabled = enabled;
            txtName.Enabled = enabled;
            txtDescription.Enabled = enabled;
            txtCategory.Enabled = enabled;
            txtPurchasePrice.Enabled = enabled;
            txtSalePrice.Enabled = enabled;
            txtStockQuantity.Enabled = enabled;
            txtMinStockLevel.Enabled = enabled;
            txtUnit.Enabled = enabled;
            txtBarcode.Enabled = enabled;
            txtNotes.Enabled = enabled;
            chkIsActive.Enabled = enabled;

            btnSave.Enabled = enabled;
            btnCancel.Enabled = enabled;
        }

        // أحداث الأزرار
        private void btnNew_Click(object sender, EventArgs e)
        {
            ClearForm();
            EnableControls(true);
            txtCode.Focus();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvProducts.CurrentRow?.DataBoundItem is Product product)
            {
                LoadProductToForm(product);
                EnableControls(true);
                txtName.Focus();
            }
            else
            {
                MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var product = GetProductFromForm();

                if (_currentProduct == null)
                {
                    // إضافة منتج جديد
                    var id = _productRepository.Add(product);
                    if (id > 0)
                    {
                        MessageBox.Show("تم حفظ المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadProducts();
                        ClearForm();
                        EnableControls(false);
                    }
                }
                else
                {
                    // تحديث منتج موجود
                    product.UpdateLastModified();
                    if (_productRepository.Update(product))
                    {
                        MessageBox.Show("تم تحديث المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadProducts();
                        ClearForm();
                        EnableControls(false);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            ClearForm();
            EnableControls(false);
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.CurrentRow?.DataBoundItem is Product product)
            {
                if (MessageBox.Show($"هل تريد حذف المنتج '{product.Name}'؟", 
                                  "تأكيد الحذف", 
                                  MessageBoxButtons.YesNo, 
                                  MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    try
                    {
                        if (_productRepository.Delete(product.Id))
                        {
                            MessageBox.Show("تم حذف المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadProducts();
                            ClearForm();
                            EnableControls(false);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchTerm))
                {
                    LoadProducts();
                }
                else
                {
                    _products = _productRepository.Search(searchTerm);
                    dgvProducts.DataSource = _products;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearch_Click(sender, e);
            }
        }

        private void dgvProducts_SelectionChanged(object sender, EventArgs e)
        {
            btnEdit.Enabled = dgvProducts.CurrentRow != null;
            btnDelete.Enabled = dgvProducts.CurrentRow != null;
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
