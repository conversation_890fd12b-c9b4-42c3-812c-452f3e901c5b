using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using SalesSystem.Data;
using SalesSystem.Models;

namespace SalesSystem.Forms
{
    /// <summary>
    /// نموذج المبيعات
    /// </summary>
    public partial class SalesForm : Form
    {
        private ProductRepository _productRepository;
        private CustomerRepository _customerRepository;
        private List<SaleItem> _saleItems;
        private Sale _currentSale;

        public SalesForm()
        {
            InitializeComponent();
            _productRepository = new ProductRepository();
            _customerRepository = new CustomerRepository();
            _saleItems = new List<SaleItem>();
            this.Text = "فاتورة مبيعات جديدة";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void SalesForm_Load(object sender, EventArgs e)
        {
            try
            {
                InitializeNewSale();
                LoadCustomers();
                LoadProducts();
                SetupDataGridView();
                UpdateTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل نموذج المبيعات: {ex.Message}", 
                              "خطأ", 
                              MessageBoxButtons.OK, 
                              MessageBoxIcon.Error);
            }
        }

        private void InitializeNewSale()
        {
            _currentSale = new Sale();
            _currentSale.InvoiceNumber = GenerateInvoiceNumber();
            _currentSale.SaleDate = DateTime.Now;
            _currentSale.Status = SaleStatus.Draft;
            _currentSale.PaymentMethod = PaymentMethod.Cash;

            lblInvoiceNumber.Text = $"رقم الفاتورة: {_currentSale.InvoiceNumber}";
            dtpSaleDate.Value = _currentSale.SaleDate;
            cmbPaymentMethod.SelectedIndex = 0; // نقدي
        }

        private string GenerateInvoiceNumber()
        {
            // يمكن تحسين هذا لاحقاً لقراءة آخر رقم من قاعدة البيانات
            return $"INV{DateTime.Now:yyyyMMdd}{DateTime.Now:HHmmss}";
        }

        private void LoadCustomers()
        {
            var customers = _customerRepository.GetActive();
            cmbCustomer.DataSource = customers;
            cmbCustomer.DisplayMember = "Name";
            cmbCustomer.ValueMember = "Id";
            
            if (customers.Count > 0)
                cmbCustomer.SelectedIndex = 0;
        }

        private void LoadProducts()
        {
            var products = _productRepository.GetActive();
            cmbProduct.DataSource = products;
            cmbProduct.DisplayMember = "Name";
            cmbProduct.ValueMember = "Id";
            
            if (products.Count > 0)
                cmbProduct.SelectedIndex = 0;
        }

        private void SetupDataGridView()
        {
            dgvItems.AutoGenerateColumns = false;
            dgvItems.Columns.Clear();

            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Product.Name",
                HeaderText = "المنتج",
                Width = 200,
                ReadOnly = true
            });

            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Quantity",
                HeaderText = "الكمية",
                Width = 80
            });

            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "UnitPrice",
                HeaderText = "سعر الوحدة",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "DiscountPercentage",
                HeaderText = "خصم %",
                Width = 80
            });

            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Total",
                HeaderText = "الإجمالي",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" },
                ReadOnly = true
            });

            // إضافة عمود حذف
            var deleteColumn = new DataGridViewButtonColumn
            {
                HeaderText = "حذف",
                Text = "حذف",
                UseColumnTextForButtonValue = true,
                Width = 60
            };
            dgvItems.Columns.Add(deleteColumn);

            dgvItems.DataSource = _saleItems;
        }

        private void UpdateTotals()
        {
            if (_currentSale == null) return;

            _currentSale.SubTotal = _saleItems.Sum(item => item.Total);
            _currentSale.CalculateTotals();

            lblSubTotal.Text = $"المجموع الفرعي: {_currentSale.SubTotal:C}";
            lblTaxAmount.Text = $"الضريبة ({_currentSale.TaxPercentage}%): {_currentSale.TaxAmount:C}";
            lblTotalAmount.Text = $"الإجمالي النهائي: {_currentSale.TotalAmount:C}";
        }

        private void btnAddItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbProduct.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار منتج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtQuantity.Focus();
                    return;
                }

                var productId = (int)cmbProduct.SelectedValue;
                var product = _productRepository.GetById(productId);
                
                if (product == null)
                {
                    MessageBox.Show("المنتج غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (product.StockQuantity < quantity)
                {
                    MessageBox.Show($"الكمية المطلوبة ({quantity}) أكبر من المتوفر في المخزون ({product.StockQuantity})", 
                                  "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من وجود المنتج في القائمة
                var existingItem = _saleItems.FirstOrDefault(item => item.ProductId == productId);
                if (existingItem != null)
                {
                    existingItem.Quantity += quantity;
                    existingItem.CalculateTotal();
                }
                else
                {
                    var saleItem = new SaleItem
                    {
                        ProductId = productId,
                        Product = product,
                        Quantity = quantity,
                        UnitPrice = product.SalePrice,
                        DiscountPercentage = 0
                    };
                    saleItem.CalculateTotal();
                    _saleItems.Add(saleItem);
                }

                // تحديث العرض
                dgvItems.DataSource = null;
                dgvItems.DataSource = _saleItems;
                UpdateTotals();

                // مسح الحقول
                txtQuantity.Text = "1";
                cmbProduct.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvItems_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            // التعامل مع زر الحذف
            if (e.ColumnIndex == dgvItems.Columns.Count - 1 && e.RowIndex >= 0) // عمود الحذف
            {
                if (MessageBox.Show("هل تريد حذف هذا العنصر؟", "تأكيد الحذف", 
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    _saleItems.RemoveAt(e.RowIndex);
                    dgvItems.DataSource = null;
                    dgvItems.DataSource = _saleItems;
                    UpdateTotals();
                }
            }
        }

        private void dgvItems_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.RowIndex < _saleItems.Count)
            {
                var item = _saleItems[e.RowIndex];
                item.CalculateTotal();
                UpdateTotals();
                dgvItems.Refresh();
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateSale()) return;

                if (MessageBox.Show("هل تريد حفظ الفاتورة؟", "تأكيد الحفظ", 
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // هنا يمكن إضافة كود حفظ الفاتورة في قاعدة البيانات
                    MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // إنشاء فاتورة جديدة
                    _saleItems.Clear();
                    InitializeNewSale();
                    dgvItems.DataSource = null;
                    dgvItems.DataSource = _saleItems;
                    UpdateTotals();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateSale()
        {
            if (cmbCustomer.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار عميل", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCustomer.Focus();
                return false;
            }

            if (_saleItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة منتجات للفاتورة", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (_saleItems.Count > 0)
            {
                if (MessageBox.Show("هل تريد إلغاء الفاتورة الحالية؟", "تأكيد الإلغاء", 
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    _saleItems.Clear();
                    InitializeNewSale();
                    dgvItems.DataSource = null;
                    dgvItems.DataSource = _saleItems;
                    UpdateTotals();
                }
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            if (_saleItems.Count > 0)
            {
                if (MessageBox.Show("هناك فاتورة غير محفوظة. هل تريد الإغلاق؟", "تأكيد الإغلاق", 
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                {
                    return;
                }
            }
            this.Close();
        }

        private void txtQuantity_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح بالأرقام والفاصلة العشرية فقط
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }

            // السماح بفاصلة عشرية واحدة فقط
            if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }

            // إضافة المنتج عند الضغط على Enter
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnAddItem_Click(sender, e);
            }
        }
    }
}
