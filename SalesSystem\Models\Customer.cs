using System;
using System.ComponentModel.DataAnnotations;

namespace SalesSystem.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "كود العميل مطلوب")]
        public string Code { get; set; }
        
        [Required(ErrorMessage = "اسم العميل مطلوب")]
        public string Name { get; set; }
        
        public CustomerType Type { get; set; }
        public string IdentityNumber { get; set; }
        public string TaxNumber { get; set; }
        
        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        public string Phone { get; set; }
        
        public string Phone2 { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Region { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal CurrentBalance { get; set; }
        public decimal DiscountPercentage { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        public DateTime? LastPurchaseDate { get; set; }
        public decimal TotalPurchases { get; set; }
        public string Notes { get; set; }

        public bool IsOverCreditLimit => CurrentBalance > CreditLimit;
        public decimal AvailableCredit => Math.Max(0, CreditLimit - CurrentBalance);

        public Customer()
        {
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
            IsActive = true;
            Type = CustomerType.Individual;
            Country = "المملكة العربية السعودية";
        }

        public void UpdateLastModified()
        {
            LastModified = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{Code} - {Name}";
        }
    }

    public enum CustomerType
    {
        Individual = 1,
        Company = 2
    }
}
