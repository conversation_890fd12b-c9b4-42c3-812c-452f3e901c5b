using System;
using System.ComponentModel.DataAnnotations;

namespace SalesSystem.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        /// <summary>
        /// معرف المنتج الفريد
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// كود المنتج
        /// </summary>
        [Required(ErrorMessage = "كود المنتج مطلوب")]
        [StringLength(50, ErrorMessage = "كود المنتج يجب أن يكون أقل من 50 حرف")]
        public string Code { get; set; }

        /// <summary>
        /// اسم المنتج
        /// </summary>
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 200 حرف")]
        public string Name { get; set; }

        /// <summary>
        /// وصف المنتج
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف المنتج يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// فئة المنتج
        /// </summary>
        [StringLength(100, ErrorMessage = "فئة المنتج يجب أن تكون أقل من 100 حرف")]
        public string Category { get; set; }

        /// <summary>
        /// سعر الشراء
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// سعر البيع
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SalePrice { get; set; }

        /// <summary>
        /// الكمية المتوفرة في المخزن
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من أو تساوي صفر")]
        public int StockQuantity { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public int MinStockLevel { get; set; }

        /// <summary>
        /// وحدة القياس
        /// </summary>
        [StringLength(20, ErrorMessage = "وحدة القياس يجب أن تكون أقل من 20 حرف")]
        public string Unit { get; set; }

        /// <summary>
        /// الباركود
        /// </summary>
        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string Barcode { get; set; }

        /// <summary>
        /// هل المنتج نشط
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// مسار صورة المنتج
        /// </summary>
        [StringLength(500, ErrorMessage = "مسار الصورة يجب أن يكون أقل من 500 حرف")]
        public string ImagePath { get; set; }

        /// <summary>
        /// نسبة الربح المحسوبة
        /// </summary>
        public decimal ProfitMargin
        {
            get
            {
                if (PurchasePrice == 0) return 0;
                return ((SalePrice - PurchasePrice) / PurchasePrice) * 100;
            }
        }

        /// <summary>
        /// هل المنتج يحتاج إعادة تموين
        /// </summary>
        public bool NeedsRestock
        {
            get
            {
                return StockQuantity <= MinStockLevel;
            }
        }

        /// <summary>
        /// قيمة المخزون الإجمالية
        /// </summary>
        public decimal TotalStockValue
        {
            get
            {
                return StockQuantity * PurchasePrice;
            }
        }

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Product()
        {
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
            IsActive = true;
            Unit = "قطعة";
        }

        /// <summary>
        /// تحديث تاريخ آخر تعديل
        /// </summary>
        public void UpdateLastModified()
        {
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// تحديث كمية المخزون
        /// </summary>
        /// <param name="quantity">الكمية الجديدة</param>
        public void UpdateStock(int quantity)
        {
            StockQuantity = quantity;
            UpdateLastModified();
        }

        /// <summary>
        /// خصم كمية من المخزون
        /// </summary>
        /// <param name="quantity">الكمية المطلوب خصمها</param>
        /// <returns>true إذا تم الخصم بنجاح</returns>
        public bool DeductStock(decimal quantity)
        {
            if (StockQuantity >= quantity)
            {
                StockQuantity -= (int)quantity;
                UpdateLastModified();
                return true;
            }
            return false;
        }

        /// <summary>
        /// إضافة كمية للمخزون
        /// </summary>
        /// <param name="quantity">الكمية المطلوب إضافتها</param>
        public void AddStock(decimal quantity)
        {
            StockQuantity += (int)quantity;
            UpdateLastModified();
        }

        /// <summary>
        /// تمثيل نصي للمنتج
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{Code} - {Name}";
        }
    }
}
