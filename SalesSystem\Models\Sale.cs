using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace SalesSystem.Models
{
    /// <summary>
    /// نموذج فاتورة المبيعات
    /// </summary>
    public class Sale
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public string InvoiceNumber { get; set; }
        
        public int CustomerId { get; set; }
        public Customer Customer { get; set; }
        
        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime SaleDate { get; set; }
        
        public DateTime DueDate { get; set; }
        public SaleStatus Status { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        
        public string Notes { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        
        public List<SaleItem> Items { get; set; }

        public bool IsFullyPaid => RemainingAmount <= 0;
        public bool IsOverdue => DueDate < DateTime.Now && RemainingAmount > 0;

        public Sale()
        {
            SaleDate = DateTime.Now;
            DueDate = DateTime.Now.AddDays(30);
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
            Status = SaleStatus.Draft;
            PaymentMethod = PaymentMethod.Cash;
            Items = new List<SaleItem>();
            TaxPercentage = 15; // ضريبة القيمة المضافة السعودية
        }

        public void CalculateTotals()
        {
            SubTotal = Items?.Sum(i => i.Total) ?? 0;
            
            if (DiscountPercentage > 0)
                DiscountAmount = SubTotal * (DiscountPercentage / 100);
            
            var afterDiscount = SubTotal - DiscountAmount;
            TaxAmount = afterDiscount * (TaxPercentage / 100);
            TotalAmount = afterDiscount + TaxAmount;
            RemainingAmount = TotalAmount - PaidAmount;
        }

        public void UpdateLastModified()
        {
            LastModified = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{InvoiceNumber} - {Customer?.Name} - {TotalAmount:C}";
        }
    }

    public enum SaleStatus
    {
        Draft = 1,
        Confirmed = 2,
        Shipped = 3,
        Delivered = 4,
        Cancelled = 5
    }

    public enum PaymentMethod
    {
        Cash = 1,
        Card = 2,
        BankTransfer = 3,
        Check = 4,
        Credit = 5
    }
}
