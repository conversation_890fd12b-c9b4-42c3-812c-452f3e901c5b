using System;
using System.ComponentModel.DataAnnotations;

namespace SalesSystem.Models
{
    /// <summary>
    /// نموذج عنصر في فاتورة المبيعات
    /// </summary>
    public class SaleItem
    {
        public int Id { get; set; }
        
        public int SaleId { get; set; }
        public Sale Sale { get; set; }
        
        public int ProductId { get; set; }
        public Product Product { get; set; }
        
        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.01, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }
        
        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }
        
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercentage { get; set; }
        
        public decimal DiscountAmount { get; set; }
        public decimal Total { get; set; }
        
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// الإجمالي قبل الخصم
        /// </summary>
        public decimal SubTotal => Quantity * UnitPrice;

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public SaleItem()
        {
            CreatedDate = DateTime.Now;
            Quantity = 1;
        }

        /// <summary>
        /// حساب الإجماليات
        /// </summary>
        public void CalculateTotal()
        {
            var subTotal = Quantity * UnitPrice;
            
            if (DiscountPercentage > 0)
                DiscountAmount = subTotal * (DiscountPercentage / 100);
            else
                DiscountAmount = 0;
            
            Total = subTotal - DiscountAmount;
        }

        /// <summary>
        /// تمثيل نصي للعنصر
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{Product?.Name} - {Quantity} × {UnitPrice:C} = {Total:C}";
        }
    }
}
