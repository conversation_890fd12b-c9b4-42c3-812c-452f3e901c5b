using System;
using System.Windows.Forms;
using SalesSystem.Forms;
using SalesSystem.Data;

namespace SalesSystem
{
    /// <summary>
    /// نقطة البداية الرئيسية للتطبيق
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // تهيئة قاعدة البيانات
                DatabaseHelper.InitializeDatabase();
                
                // تشغيل النموذج الرئيسي
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تشغيل التطبيق: " + ex.Message,
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
        }
    }
}
