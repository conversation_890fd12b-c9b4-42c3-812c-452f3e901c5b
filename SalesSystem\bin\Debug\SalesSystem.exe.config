<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    
    <connectionStrings>
        <add name="SalesSystemDB" 
             connectionString="Data Source=SalesSystem.db;Version=3;" 
             providerName="System.Data.SQLite" />
    </connectionStrings>
    
    <appSettings>
        <!-- إعدادات التطبيق -->
        <add key="CompanyName" value="شركة المبيعات المتقدمة" />
        <add key="CompanyAddress" value="الرياض، المملكة العربية السعودية" />
        <add key="CompanyPhone" value="+966-11-1234567" />
        <add key="CompanyEmail" value="<EMAIL>" />
        <add key="TaxNumber" value="123456789012345" />
        <add key="Currency" value="ريال سعودي" />
        <add key="CurrencySymbol" value="ر.س" />
        
        <!-- إعدادات النظام -->
        <add key="AutoBackup" value="true" />
        <add key="BackupInterval" value="24" />
        <add key="MaxBackupFiles" value="30" />
        <add key="EnableLogging" value="true" />
        <add key="LogLevel" value="Info" />
        
        <!-- إعدادات الطباعة -->
        <add key="DefaultPrinter" value="" />
        <add key="PrintCopies" value="1" />
        <add key="AutoPrint" value="false" />
        
        <!-- إعدادات الفواتير -->
        <add key="InvoicePrefix" value="INV" />
        <add key="InvoiceStartNumber" value="1000" />
        <add key="TaxRate" value="15" />
    </appSettings>
</configuration>
