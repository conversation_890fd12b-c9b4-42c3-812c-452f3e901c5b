@echo off
chcp 65001 >nul
echo ========================================
echo       إعداد نظام إدارة المبيعات
echo ========================================
echo.

cd /d "%~dp0"

echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود .NET Framework
echo التحقق من .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ .NET Framework 4.8 متوفر
) else (
    echo ✗ .NET Framework 4.8 غير متوفر
    echo يرجى تحميل وتثبيت .NET Framework 4.8 من موقع Microsoft
    pause
    exit /b 1
)

echo.

REM التحقق من وجود Visual Studio أو MSBuild
echo التحقق من أدوات التطوير...
where devenv >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Visual Studio متوفر
    set BUILD_TOOL=devenv
) else (
    where msbuild >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ MSBuild متوفر
        set BUILD_TOOL=msbuild
    ) else (
        echo ✗ لم يتم العثور على Visual Studio أو MSBuild
        echo يرجى تثبيت Visual Studio 2019 أو أحدث
        pause
        exit /b 1
    )
)

echo.

REM التحقق من ملفات المشروع
echo التحقق من ملفات المشروع...
if exist "SalesSystem.sln" (
    echo ✓ ملف الحل موجود
) else (
    echo ✗ ملف الحل غير موجود
    pause
    exit /b 1
)

if exist "SalesSystem\SalesSystem.csproj" (
    echo ✓ ملف المشروع موجود
) else (
    echo ✗ ملف المشروع غير موجود
    pause
    exit /b 1
)

echo.
echo ========================================
echo جميع المتطلبات متوفرة!
echo ========================================
echo.

echo الخيارات المتاحة:
echo 1. فتح المشروع في Visual Studio
echo 2. بناء المشروع
echo 3. تشغيل النظام
echo 4. إنشاء نسخة قابلة للتوزيع
echo 5. خروج
echo.

set /p choice="اختر رقم الخيار (1-5): "

if "%choice%"=="1" goto open_vs
if "%choice%"=="2" goto build
if "%choice%"=="3" goto run
if "%choice%"=="4" goto publish
if "%choice%"=="5" goto exit

echo خيار غير صحيح
pause
goto exit

:open_vs
echo فتح المشروع في Visual Studio...
start "" "SalesSystem.sln"
goto exit

:build
echo بناء المشروع...
if "%BUILD_TOOL%"=="devenv" (
    devenv SalesSystem.sln /build Release
) else (
    msbuild SalesSystem.sln /p:Configuration=Release
)
if %errorlevel% equ 0 (
    echo ✓ تم بناء المشروع بنجاح
) else (
    echo ✗ فشل في بناء المشروع
)
pause
goto exit

:run
echo تشغيل النظام...
if exist "SalesSystem\bin\Release\SalesSystem.exe" (
    start "" "SalesSystem\bin\Release\SalesSystem.exe"
) else if exist "SalesSystem\bin\Debug\SalesSystem.exe" (
    start "" "SalesSystem\bin\Debug\SalesSystem.exe"
) else (
    echo لم يتم العثور على ملف التشغيل
    echo يرجى بناء المشروع أولاً
    pause
)
goto exit

:publish
echo إنشاء نسخة قابلة للتوزيع...
if not exist "Release" mkdir Release
if "%BUILD_TOOL%"=="devenv" (
    devenv SalesSystem.sln /build Release
) else (
    msbuild SalesSystem.sln /p:Configuration=Release
)

if exist "SalesSystem\bin\Release\SalesSystem.exe" (
    copy "SalesSystem\bin\Release\*.*" "Release\" >nul
    echo ✓ تم إنشاء النسخة في مجلد Release
) else (
    echo ✗ فشل في إنشاء النسخة
)
pause
goto exit

:exit
echo.
echo شكراً لاستخدام نظام إدارة المبيعات
pause
