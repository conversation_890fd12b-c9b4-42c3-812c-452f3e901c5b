@echo off
echo ========================================
echo       نظام إدارة المبيعات
echo ========================================
echo.
echo جاري تشغيل النظام...
echo.

cd /d "%~dp0"

REM التحقق من وجود ملف المشروع
if not exist "SalesSystem\SalesSystem.csproj" (
    echo خطأ: ملف المشروع غير موجود
    pause
    exit /b 1
)

REM محاولة تشغيل النظام باستخدام Visual Studio
if exist "SalesSystem.sln" (
    echo فتح المشروع في Visual Studio...
    start "" "SalesSystem.sln"
) else (
    echo خطأ: ملف الحل غير موجود
    pause
    exit /b 1
)

echo.
echo تم فتح المشروع في Visual Studio
echo يمكنك الآن تشغيل النظام من خلال الضغط على F5
echo أو Build > Start Debugging
echo.
pause
