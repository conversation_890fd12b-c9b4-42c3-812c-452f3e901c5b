========================================
       دليل المستخدم السريع
    نظام إدارة المبيعات - الإصدار 1.0
========================================

🚀 البدء السريع:
================

1. تشغيل النظام:
   - انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
   - أو افتح "SalesSystem.sln" في Visual Studio واضغط F5

2. أول استخدام:
   - سيتم إنشاء قاعدة البيانات تلقائياً
   - ستظهر الشاشة الرئيسية مع لوحة المعلومات

📦 إدارة المنتجات:
==================

إضافة منتج جديد:
1. اضغط على "المنتجات" من الشاشة الرئيسية
2. اضغط "جديد"
3. أدخل البيانات المطلوبة:
   - كود المنتج (مطلوب)
   - اسم المنتج (مطلوب)
   - سعر الشراء
   - سعر البيع
   - الكمية في المخزون
4. اضغط "حفظ"

البحث عن منتج:
- استخدم مربع البحث في أعلى النموذج
- يمكن البحث بالاسم أو الكود

تعديل منتج:
1. اختر المنتج من القائمة
2. اضغط "تعديل"
3. عدّل البيانات المطلوبة
4. اضغط "حفظ"

👥 إدارة العملاء:
==================

إضافة عميل جديد:
1. اضغط على "العملاء" من الشاشة الرئيسية
2. اضغط "جديد"
3. أدخل البيانات:
   - كود العميل (مطلوب)
   - اسم العميل (مطلوب)
   - نوع العميل (فرد/شركة)
   - رقم الهاتف (مطلوب)
   - باقي البيانات اختيارية
4. اضغط "حفظ"

إدارة الحد الائتماني:
- أدخل الحد الائتماني المسموح للعميل
- سيتم تنبيهك عند تجاوز الحد

🧾 إنشاء فاتورة مبيعات:
========================

خطوات إنشاء فاتورة:
1. اضغط على "المبيعات" من الشاشة الرئيسية
2. اختر العميل من القائمة المنسدلة
3. إضافة منتجات للفاتورة:
   - اختر المنتج
   - أدخل الكمية
   - اضغط "إضافة"
4. مراجعة الفاتورة:
   - تحقق من المنتجات والكميات
   - راجع المجاميع والضريبة
5. اضغط "حفظ" لحفظ الفاتورة

حذف منتج من الفاتورة:
- اضغط على زر "حذف" بجانب المنتج في القائمة

تعديل الكمية أو الخصم:
- اضغط على الخلية المطلوبة في الجدول
- عدّل القيمة مباشرة

📊 لوحة المعلومات:
===================

تعرض لوحة المعلومات الرئيسية:
- إجمالي عدد المنتجات
- إجمالي عدد العملاء
- المنتجات منخفضة المخزون
- إحصائيات سريعة

تحديث البيانات:
- يتم تحديث البيانات تلقائياً كل دقيقة
- أو اضغط F5 للتحديث الفوري

⚙️ الإعدادات والتخصيص:
========================

ضريبة القيمة المضافة:
- افتراضياً 15% (حسب النظام السعودي)
- يمكن تعديلها من ملف App.config

العملة:
- افتراضياً الريال السعودي
- يمكن تغييرها من الإعدادات

ترقيم الفواتير:
- تلقائي بصيغة: INV + التاريخ والوقت
- مثال: INV20241226143022

🔧 حل المشاكل الشائعة:
=======================

مشكلة: "خطأ في قاعدة البيانات"
الحل: تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

مشكلة: "النص العربي لا يظهر بشكل صحيح"
الحل: تأكد من تثبيت خط Tahoma في النظام

مشكلة: "البرنامج بطيء"
الحل: 
- تأكد من توفر مساحة كافية على القرص الصلب
- أغلق البرامج غير المستخدمة
- أعد تشغيل البرنامج

مشكلة: "لا يمكن حفظ البيانات"
الحل:
- تأكد من عدم تشغيل نسخة أخرى من البرنامج
- تحقق من صلاحيات المجلد
- أعد تشغيل البرنامج كمدير

📁 ملفات النظام المهمة:
========================

SalesSystem.db - قاعدة البيانات الرئيسية
App.config - ملف الإعدادات
SalesSystem.exe - ملف التشغيل الرئيسي

⚠️ نصائح مهمة:
================

1. النسخ الاحتياطي:
   - انسخ ملف SalesSystem.db بانتظام
   - احتفظ بنسخة احتياطية في مكان آمن

2. الأمان:
   - لا تشارك ملف قاعدة البيانات مع أشخاص غير مخولين
   - استخدم كلمات مرور قوية للنظام

3. الأداء:
   - نظف قاعدة البيانات بانتظام
   - احذف البيانات القديمة غير المطلوبة

4. التحديثات:
   - تحقق من وجود تحديثات للنظام
   - اقرأ ملاحظات الإصدار قبل التحديث

📞 الدعم والمساعدة:
====================

للحصول على مساعدة إضافية:
- راجع ملف README.md للتفاصيل التقنية
- تحقق من رسائل الخطأ في النظام
- استخدم أدوات التصحيح في Visual Studio

========================================
        شكراً لاستخدام النظام!
========================================
